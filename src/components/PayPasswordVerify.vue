<template>
  <uni-popup 
    ref="popupRef" 
    type="bottom" 
    :mask-click="false"
    border-radius="24rpx 24rpx 0 0"
    background-color="#ffffff"
    @change="handlePopupChange"
  >
    <view class="pay-password-container">
      <!-- 头部区域 -->
      <view class="header">
        <view class="title">请输入支付密码</view>
        <view class="close-btn" @click="handleClose">
          <uni-icons type="closeempty" size="24" color="#999999"></uni-icons>
        </view>
      </view>
      
      <!-- 密码输入区域 -->
      <view class="password-section">
        <!-- 密码显示框 -->
        <view class="password-display">
          <view 
            class="password-dot" 
            v-for="(item, index) in 6" 
            :key="index"
            :class="{ 'filled': index < password.length }"
          >
            <text v-if="index < password.length" class="dot-text">*</text>
          </view>
        </view>
        
        <!-- 提示文字 -->
        <view class="password-tip">
          <text class="tip-text">密码为6位数字</text>
        </view>
        
        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-message">
          <text class="error-text">{{ errorMessage }}</text>
        </view>
      </view>
      
      <!-- 数字键盘 -->
      <view class="keyboard">
        <view class="keyboard-row" v-for="(row, rowIndex) in keyboardLayout" :key="rowIndex">
          <view 
            class="keyboard-key" 
            v-for="(key, keyIndex) in row" 
            :key="keyIndex"
            :class="{ 'key-delete': key === 'delete', 'key-disabled': key === '' }"
            @click="handleKeyPress(key)"
          >
            <text v-if="key === 'delete'" class="delete-icon">⌫</text>
            <text v-else-if="key !== ''" class="key-text">{{ key }}</text>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

// 组件属性定义
const props = defineProps({
  // 是否显示弹窗
  visible: {
    type: Boolean,
    default: false
  },
  // 验证接口函数
  verifyFunction: {
    type: Function,
    default: null
  }
})

// 组件事件定义
const emit = defineEmits([
  'update:visible',  // 更新显示状态
  'success',         // 验证成功
  'failed',          // 验证失败
  'cancel'           // 取消操作
])

// 响应式数据
const popupRef = ref(null)
const password = ref('')
const errorMessage = ref('')
const isVerifying = ref(false)

// 数字键盘布局
const keyboardLayout = reactive([
  ['1', '2', '3'],
  ['4', '5', '6'], 
  ['7', '8', '9'],
  ['', '0', 'delete']
])

/**
 * 打开弹窗
 */
const openPopup = () => {
  resetPassword()
  popupRef.value?.open()
}

/**
 * 关闭弹窗
 */
const closePopup = () => {
  popupRef.value?.close()
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    openPopup()
  } else {
    closePopup()
  }
}, { immediate: true })

// 监听密码输入完成
watch(() => password.value, (newVal) => {
  // 清除错误信息
  if (errorMessage.value) {
    errorMessage.value = ''
  }

  // 密码输入完成时自动验证
  if (newVal.length === 6) {
    handlePasswordComplete()
  }
})

/**
 * 重置密码输入
 */
const resetPassword = () => {
  password.value = ''
  errorMessage.value = ''
  isVerifying.value = false
}

/**
 * 处理键盘按键点击
 * @param {string} key - 按键值
 */
const handleKeyPress = (key) => {
  if (isVerifying.value) return
  
  if (key === 'delete') {
    // 删除最后一位
    if (password.value.length > 0) {
      password.value = password.value.slice(0, -1)
    }
  } else if (key !== '' && password.value.length < 6) {
    // 添加数字
    password.value += key
  }
}

/**
 * 密码输入完成处理
 */
const handlePasswordComplete = async () => {
  if (password.value.length !== 6) return
  
  try {
    isVerifying.value = true
    
    // 如果提供了验证函数，则调用验证
    if (props.verifyFunction && typeof props.verifyFunction === 'function') {
      const result = await props.verifyFunction(password.value)
      if (result) {
        handleVerifySuccess()
      } else {
        handleVerifyFailed('支付密码错误')
      }
    } else {
      // 模拟验证过程
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟验证结果（实际开发中应该调用真实的验证接口）
      if (password.value === '123456') {
        handleVerifySuccess()
      } else {
        handleVerifyFailed('支付密码错误')
      }
    }
    
  } catch (error) {
    console.error('支付密码验证失败:', error)
    handleVerifyFailed('验证失败，请重试')
  } finally {
    isVerifying.value = false
  }
}

/**
 * 验证成功处理
 */
const handleVerifySuccess = () => {
  emit('success', password.value)
  handleClose()
}

/**
 * 验证失败处理
 * @param {string} message - 错误信息
 */
const handleVerifyFailed = (message) => {
  errorMessage.value = message
  password.value = ''
  emit('failed', message)
  
  // 震动反馈
  uni.vibrateShort()
}

/**
 * 关闭弹窗处理
 */
const handleClose = () => {
  resetPassword()
  emit('update:visible', false)
  emit('cancel')
}

/**
 * 弹窗状态变化处理
 * @param {object} e - 事件对象
 */
const handlePopupChange = (e) => {
  if (!e.show) {
    emit('update:visible', false)
  }
}

// 暴露方法给父组件
defineExpose({
  open: openPopup,
  close: closePopup,
  reset: resetPassword
})
</script>

<style lang="scss" scoped>
.pay-password-container {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 头部区域 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    flex: 1;
    text-align: center;
  }
  
  .close-btn {
    position: absolute;
    right: 32rpx;
    top: 40rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 密码输入区域 */
.password-section {
  padding: 60rpx 32rpx 40rpx;
  
  .password-display {
    display: flex;
    justify-content: center;
    gap: 24rpx;
    margin-bottom: 32rpx;
    
    .password-dot {
      width: 80rpx;
      height: 80rpx;
      border: 2rpx solid #e0e0e0;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;
      transition: all 0.3s ease;
      
      &.filled {
        border-color: #00B781;
        background-color: #f0f8ff;
      }
      
      .dot-text {
        font-size: 48rpx;
        color: #333333;
        font-weight: bold;
      }
    }
  }
  
  .password-tip {
    text-align: center;
    margin-bottom: 16rpx;
    
    .tip-text {
      font-size: 28rpx;
      color: #999999;
    }
  }
  
  .error-message {
    text-align: center;
    
    .error-text {
      font-size: 28rpx;
      color: #ff4757;
    }
  }
}

/* 数字键盘 */
.keyboard {
  padding: 0 32rpx 40rpx;
  
  .keyboard-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .keyboard-key {
    width: 200rpx;
    height: 100rpx;
    background-color: #f8f9fa;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    &:active:not(.key-disabled) {
      background-color: #e9ecef;
      transform: scale(0.95);
    }
    
    &.key-delete {
      background-color: #f1f3f4;
      
      .delete-icon {
        font-size: 36rpx;
        color: #666666;
      }
    }
    
    &.key-disabled {
      background-color: transparent;
    }
    
    .key-text {
      font-size: 48rpx;
      font-weight: 500;
      color: #333333;
    }
  }
}

/* 验证中状态 */
.verifying {
  .keyboard-key {
    opacity: 0.6;
    pointer-events: none;
  }
}
</style>
